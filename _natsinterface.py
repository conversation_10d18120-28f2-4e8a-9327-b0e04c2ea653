__all__ = ["NatsInterface"]

from typing import Any

from tradertools.clients import NatsC<PERSON>, get_nats_client




class NatsInterface:
    def __init__(self, client_name: str, strategy_name: str = "hmmer"):
        
        
        self.nats: NatsClient = get_nats_client(client_name)
        self.strategy_name = strategy_name
        
        self._initialize_bucket()
        
        
    
    def _initialize_bucket(self):
        self.nats.create_kv_bucket(self.strategy_name)
        
    
    def get(self, key: str) -> Any:
        val = self.nats.get_kv(self.strategy_name, key)
        if val is None:
            return None
        return val
    
    
    def put(self, key: str, value: Any):
        self.nats.put_kv(self.strategy_name, key, value)
    
    
    def connect(self):
        self.nats.connect()
    
    def disconnect(self):
        self.nats.disconnect()

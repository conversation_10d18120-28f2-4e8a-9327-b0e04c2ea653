services:
  hmmer-dash:
    # Use published image instead of building locally
    image: ghcr.io/luneanalytics/hmmer_dash:latest
    # build: .  # Comment out for production
    container_name: hmmer-dash
    restart: unless-stopped
    network_mode: "host"
    environment:
      - ENV=development  # Use development since NATS is on localhost
      - NATS_CLIENT=nats_dev
      - POSTGRES_CLIENT=postgres_dev
    volumes:
      - ./.env:/app/.env:ro
      - ./connections.yaml:/app/connections.yaml:ro
      - ./logs:/app/logs
      - ./data:/app/data
    
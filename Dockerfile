# Build stage
FROM python:3.13-slim AS builder
ENV POETRY_NO_INTERACTION=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache \
    POETRY_VENV_IN_PROJECT=1

RUN apt-get update && apt-get install -y --no-install-recommends \
    git gcc && \
    pip install poetry==1.6.1 && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY pyproject.toml poetry.lock* ./

# Create virtual environment first
RUN poetry env use python3

# Install dependencies using BuildKit secrets for private repos
RUN --mount=type=secret,id=ghcr_pat \
    if [ -f /run/secrets/ghcr_pat ]; then \
        echo "Secret file found" && \
        GHCR_PAT=$(cat /run/secrets/ghcr_pat) && \
        echo "Token length: $(echo -n "$GHCR_PAT" | wc -c)" && \
        # Configure git to use the token and disable interactive prompts \
        git config --global credential.helper '' && \
        git config --global url."https://${GHCR_PAT}@github.com/".insteadOf "https://github.com/" && \
        # Install dependencies with Poetry directly \
        GIT_TERMINAL_PROMPT=0 poetry install --only=main --no-dev --no-root && \
        # Clean up git config \
        git config --global --unset url."https://${GHCR_PAT}@github.com/".insteadOf && \
        git config --global --unset credential.helper; \
    else \
        echo "WARNING: No GitHub token provided, attempting install without authentication" && \
        poetry install --only=main --no-dev --no-root; \
    fi

# Runtime stage
FROM python:3.13-slim

# OCI Labels for GitHub Container Registry
LABEL org.opencontainers.image.source="https://github.com/yourusername/hmmer_dash"
LABEL org.opencontainers.image.url="https://github.com/yourusername/hmmer_dash"
LABEL org.opencontainers.image.description="HMMER dashboard application for protein sequence analysis"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.title="HMMER Dash"
LABEL org.opencontainers.image.vendor="Your Organization"

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH="/app" \
    POETRY_VENV_IN_PROJECT=1

WORKDIR /app

# Install Poetry in runtime stage
RUN pip install poetry==1.6.1

# Copy pyproject.toml and poetry.lock from builder
COPY --from=builder /app/pyproject.toml /app/poetry.lock* ./

# Copy the virtual environment from builder
COPY --from=builder /app/.venv /app/.venv

# Make sure we use venv
ENV PATH="/app/.venv/bin:$PATH"

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data

# Expose port 8050
EXPOSE 8050

# Run the application with gunicorn
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:8050", "dash_overview:app"]
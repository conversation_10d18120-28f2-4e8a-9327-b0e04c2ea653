# Build stage
FROM python:3.12-slim AS builder
ENV POETRY_NO_INTERACTION=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

RUN apt-get update && apt-get install -y --no-install-recommends \
    git gcc && \
    pip install poetry==1.6.1 && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY pyproject.toml poetry.lock* ./

# Create packages directory
RUN mkdir -p /app/packages

# Install dependencies using BuildKit secrets for private repos
RUN --mount=type=secret,id=ghcr_pat \
    if [ -f /run/secrets/ghcr_pat ]; then \
        echo "Secret file found" && \
        GHCR_PAT=$(cat /run/secrets/ghcr_pat) && \
        echo "Token length: $(echo -n "$GHCR_PAT" | wc -c)" && \
        # Configure git to use the token and disable interactive prompts \
        git config --global credential.helper '' && \
        git config --global url."https://${GHCR_PAT}@github.com/".insteadOf "https://github.com/" && \
        # Export poetry dependencies \
        poetry export -f requirements.txt --output requirements.txt --without-hashes --only=main && \
        echo "Generated requirements.txt:" && \
        cat requirements.txt && \
        # Install with pip \
        GIT_TERMINAL_PROMPT=0 pip install --no-cache-dir --target /app/packages -r requirements.txt && \
        # Clean up git config \
        git config --global --unset url."https://${GHCR_PAT}@github.com/".insteadOf && \
        git config --global --unset credential.helper; \
    else \
        echo "WARNING: No GitHub token provided, attempting install without authentication" && \
        poetry export -f requirements.txt --output requirements.txt --without-hashes --only=main && \
        pip install --no-cache-dir --target /app/packages -r requirements.txt; \
    fi

# Runtime stage
FROM python:3.13-slim

# OCI Labels for GitHub Container Registry
LABEL org.opencontainers.image.source="https://github.com/yourusername/hmmer_dash"
LABEL org.opencontainers.image.url="https://github.com/yourusername/hmmer_dash"
LABEL org.opencontainers.image.description="HMMER dashboard application for protein sequence analysis"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.title="HMMER Dash"
LABEL org.opencontainers.image.vendor="Your Organization"

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH="/app:/app/packages" \
    PATH="/app/packages/bin:$PATH"

WORKDIR /app

# Copy installed packages from builder
COPY --from=builder /app/packages /app/packages

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data

# Expose port 8050
EXPOSE 8050

# Run the application with gunicorn
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:8050", "dash_overview:app"]
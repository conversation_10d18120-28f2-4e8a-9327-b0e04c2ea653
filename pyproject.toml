[project]
name = "hmmer-dash"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "pandas (>=2.3.2,<3.0.0)",
    "dash (>=3.2.0,<4.0.0)",
    "dash-bootstrap-components (>=2.0.4,<3.0.0)",
    "dash-ag-grid (>=32.3.2,<33.0.0)",
    "plotly (>=6.3.0,<7.0.0)",
    "dash-bootstrap-templates (>=2.1.0,<3.0.0)",
    "gunicorn (>=23.0.0,<24.0.0)",
    "setproctitle (>=1.3.7,<2.0.0)",
    "tradertools @ git+https://github.com/LuneAnalytics/tradertools.git"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
ignore = [
    "E402", # module level import not at top of file
    "F403", # while expected an indented block
]
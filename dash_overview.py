from dotenv import load_dotenv
load_dotenv()

import os
import sys
from pathlib import Path

from setproctitle import setproctitle

setproctitle("hmmer_dash")

# Add the src directory to the Python path so we can import hmmer modules
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

import pandas as pd

import dash
from dash import dcc, html, Input, Output
import dash_bootstrap_components as dbc
import dash_bootstrap_templates as dbt
import dash_ag_grid as dag

import plotly.graph_objects as go


dbt.load_figure_template("minty_dark")
DBC_THEME = dbc.themes.MINTY

dbc_css = "https://cdn.jsdelivr.net/gh/AnnMarieW/dash-bootstrap-templates/dbc.min.css"

#aggrid defaults
AG_COLUMN_SIZE = "responsiveSizeToFit"
AG_DEFAULT_COL_DEF = {
    # "resizable": True,  
    "sortable": True, 
    # "filter": False,
    "cellStyle": { # vertically center cell
        "display": "flex",
        "alignItems": "center",
        "paddingTop": 0,
        "paddingBottom": 0,
    }
}
AG_DASH_GRID_OPTIONS = {
    "domLayout": "autoHeight",
    "rowHeight": 22,
}
AG_CLASS_NAME = "ag-theme-balham-auto-dark"
# AG_CLASS_NAME = "dbc dbc-ag-grid"
AG_STYLE = {}

ENV = os.getenv("ENV", None)
if ENV is None:
    raise ValueError("ENV not set")
if ENV not in ["production", "development"]:
    raise ValueError(f"Invalid ENV: {ENV}")

print(f"ENV: {ENV}")

# Initialize connections
from _natsinterface import NatsInterface
from tradertools.clients import get_pg_client

if ENV == "production":
    nats_interface = NatsInterface("nats_prod")
    pg = get_pg_client("postgres_prod")
else:
    nats_interface = NatsInterface("nats_dev")
    pg = get_pg_client("postgres_dev")
    
nats = nats_interface.nats



# Initialize Dash app
app = dash.Dash(__name__, external_stylesheets=[DBC_THEME, dbc.icons.FONT_AWESOME, dbc_css])
app.title = "HMMER Overview"




# Define AG Grid column definitions
def get_column_defs(df):
    """Generate column definitions for AG Grid from dataframe"""
    return [{"field": col, "sortable": True, "filter": True} for col in df.columns]

# Layout
header_elements = []
header_elements.append(html.H3("Hmmer\nOverview", style={  #"📈 Hmmer Overview"
        'flex': 1,
        'text-align': 'left',
        'font-weight': 'bold',
        # 'padding-right': '50px'
    })
)

header_elements.append(html.Div(id='running-status', style={
    'flex': 1,
    'maxWidth': '150px',
}))
header_elements.append(html.Div(id='ib-connection-status', style={
    'flex': 1,
    'maxWidth': '150px',
}))
header_elements.append(html.Div(
    [
        html.H3(id='lifetime-pl', style={'text-align': 'right'}),
        html.H3(id='daily-pl', style={'text-align': 'right'}),
    ],
    style={'flex': 1})
)


    # is simulation
elements = []
if ENV == "development":
    elements = [html.Div(dbc.Alert("⚠️ SIMULATION", color="warning", style={'text-align': 'center'}), 
        style={
            'justify-content': 'center',
        })
    ]

elements.extend([
    # dark mode
    # color_mode_switch,
    # Header
    html.Div(header_elements, style={
        'display': 'flex',
        'justify-content': 'space-between',
        # 'padding': '20px',  
        # 'margin-bottom': '20px'
        },
    ),
    
    html.Div([
        # html.H4("Performance"),
        dcc.Graph(id='performance-chart')
    ], style={
        'padding-bottom': '20px'
    }),
    
    # Products Section
    html.Div([
        html.H4("Products"),
        dag.AgGrid(
            id='products-grid',
            columnSize=AG_COLUMN_SIZE,  # type: ignore
            defaultColDef=AG_DEFAULT_COL_DEF, # type: ignore
            dashGridOptions=AG_DASH_GRID_OPTIONS, # type: ignore
            className=AG_CLASS_NAME, # type: ignore
            style=AG_STYLE, # type: ignore
        )
    ], style={
        'padding-bottom': '20px'
    }),
    
    # Open Orders Section
    html.Div([
        html.H4("Open Orders"),
        dag.AgGrid(
            id='open-orders-grid',
            columnSize=AG_COLUMN_SIZE,  # type: ignore
            defaultColDef=AG_DEFAULT_COL_DEF, # type: ignore
            dashGridOptions=AG_DASH_GRID_OPTIONS, # type: ignore
            className=AG_CLASS_NAME, # type: ignore
            style=AG_STYLE, # type: ignore
        )], 
        style={
            'padding-bottom': '20px'
        }
    ),
    
    # Fills Section
    html.Div([
        html.H4("Fills"),
        dag.AgGrid(
            id='fills-grid',
            columnSize=AG_COLUMN_SIZE,  # type: ignore
            defaultColDef=AG_DEFAULT_COL_DEF, # type: ignore
            dashGridOptions=AG_DASH_GRID_OPTIONS, # type: ignore
            className=AG_CLASS_NAME, # type: ignore
            style=AG_STYLE, # type: ignore
        )], 
        style={
            'padding-bottom': '20px'
        }
    ),
    
    # Product Selection and Parameters
    html.Div([
        html.Div([
            html.H4("Select Product"),
            dbc.Select(
                id='product-dropdown',
                options=[],
                value=None,
                
                style={'width': '100%'}
            ),
            html.H4("Parameters"),
            dag.AgGrid(
                id='parameters-grid',
                 columnSize=AG_COLUMN_SIZE,  # type: ignore
                defaultColDef=AG_DEFAULT_COL_DEF, # type: ignore
                dashGridOptions=AG_DASH_GRID_OPTIONS, # type: ignore
                className=AG_CLASS_NAME, # type: ignore
                style=AG_STYLE, # type: ignore
            )], 
            style={
                'width': '300px', 
                # 'display': 'inline-block', 
                'vertical-align': 'top', 
                'flex-shrink': 0,
                # 'padding': '20px'
            }
        ),
        
        # Chart and Data Tabs
        html.Div(
            id='tab-content',
            style={
                'flex': 1,
                'min-width': '0',
                # 'display': 'inline-block', 
                'padding': '20px'
            }
        ),],
        style={
            'display': 'flex',
            # 'flex-direction': 'row',
            'width': '100%'
        }
    ),
    
    # Store components for data
    dcc.Store(id='live-data-store'),
    dcc.Store(id='selected-product-store'),
    dcc.Store(id='performance-data'),
    dcc.Store(id='bar-data'),
    
    # Interval component for live updates (60 seconds)
    dcc.Interval(
        id='interval-component',
        interval=60*1000,  # in milliseconds
        n_intervals=0
    )])
app.layout = dbc.Container(
    elements,
    fluid=True,
    className="dbc dbc-ag-grid",
    style={
        'padding': '20px'
    }
)

# Callback for fetching live data
@app.callback(
    [
        Output('running-status', 'children'),
        Output('live-data-store', 'data'),
        Output('lifetime-pl', 'children'),
        Output('daily-pl', 'children'),
        Output('products-grid', 'rowData'),
        Output('products-grid', 'columnDefs'),
        Output('open-orders-grid', 'rowData'),
        Output('open-orders-grid', 'columnDefs'),
        Output('fills-grid', 'rowData'),
        Output('fills-grid', 'columnDefs'),
        Output('product-dropdown', 'options'),
        Output('ib-connection-status', 'children')
    ],
    [
        Input('interval-component', 'n_intervals')
    ]
)
def update_live_data(n):
    # Fetch data from NATS
    try:
        summary = nats.request('strategies.hmmer.requests', 'summary')
        products_data = nats.request('strategies.hmmer.requests', 'summary_products')
        open_orders_data = nats.request('strategies.hmmer.requests', 'openOrders')
        fills_data = nats.request('strategies.hmmer.requests', 'fills')
        ib_connected = nats.request('strategies.hmmer.requests', 'ib_connected')
    except Exception as e:
        print(f"Error fetching live data: {e}")
        return (
            dbc.Alert("❌ Algo Not Running", color="danger", style={'text-align': 'center'}), 
            {}, 
            "Error", 
            "Error", 
            [], 
            [], 
            [], 
            [], 
            [], 
            [], 
            [],
            dbc.Alert("Error Fetching Data", color="danger", style={'text-align': 'center'})
        )
    
    
    # p.update(update_bars=True)
    
    # Convert to DataFrames
    products_df = pd.DataFrame(products_data)
    products_df['product'] = products_df.index if 'product' not in products_df.columns else products_df['product']
    
    open_orders_df = pd.DataFrame(open_orders_data)
    if 'orderId' in open_orders_df.columns:
        open_orders_df['orderId'] = open_orders_df['orderId']
    
    fills_df = pd.DataFrame(fills_data)
    if 'datetime' in fills_df.columns:
        fills_df['datetime'] = fills_df['datetime']
    
    # Get product names for dropdown
    prd_names = products_df['product'].unique().tolist() if 'product' in products_df.columns else []
    dropdown_options = [{'label': p, 'value': p} for p in prd_names]
    
    # Prepare column definitions
    products_cols = [{"field": col, "sortable": True} for col in products_df.columns]
    orders_cols = [{"field": col, "sortable": True} for col in open_orders_df.columns]
    fills_cols = [{"field": col, "sortable": True} for col in fills_df.columns]
    
    # Format lifetime PL
    pl_text = f"Lifetime PL: ${summary.get('totalPL', 0):.2f}" # type: ignore
    dailyPL_text = f"Daily PL: ${summary.get('dailyPL', 0):.2f}" # type: ignore
    
    print(summary)
    print(pl_text)
    print(dailyPL_text)
    
    ib_alert = dbc.Alert("IB Disconnected", color="danger", style={'text-align': 'center'}) if not ib_connected else dbc.Alert("IB Connected", color="success", style={'text-align': 'center'})
    
    return (
        dbc.Alert("Algo Running", color="success", style={'text-align': 'center'}),
        {'summary': summary, 'products': products_data},
        pl_text,
        dailyPL_text,
        products_df.to_dict('records'),
        products_cols,
        open_orders_df.to_dict('records'),
        orders_cols,
        fills_df.to_dict('records'),
        fills_cols,
        dropdown_options,
        ib_alert,
    )

# Callback for updating parameters when product is selected
@app.callback(
    [Output('parameters-grid', 'rowData'),
     Output('parameters-grid', 'columnDefs'),
     Output('selected-product-store', 'data')],
    [Input('product-dropdown', 'value')]
)
def update_parameters(selected_product):
    if not selected_product:
        return [], [], None
    
    # Query parameters from database
    query = f"SELECT * FROM hmmer.parameters WHERE product = '{selected_product}'"
    params_df = pd.DataFrame(pg.query(query))
    
    # Remove timestamp columns
    cols_to_remove = ['created_at', 'updated_at']
    for col in cols_to_remove:
        if col in params_df.columns:
            del params_df[col]
    
    # Transpose for display
    params_transposed = params_df.T.reset_index()
    params_transposed.columns = ['Parameter', 'Value']
    
    # Column definitions
    param_cols = [
        {"field": "Parameter", "sortable": False},
        {"field": "Value", "sortable": False}
    ]
    
    return params_transposed.to_dict('records'), param_cols, selected_product


# Callback for updating performance chart
@app.callback(
    Output('performance-data', 'data'),
    [Input('interval-component', 'n_intervals')] 
)
def update_performance_data(n):
    perf_data = pg.query("SELECT * FROM hmmer.performance")
    return perf_data


@app.callback(
    Output('bar-data', 'data'),  # Remove the list brackets and comma
    [Input('product-dropdown', 'value')]
)
def update_bar_data(selected_product):
    if not selected_product:
        return []
    query = f"""
    SELECT * FROM hmmer.allbars 
    WHERE prd = '{selected_product}' 
    AND dt >= NOW() - INTERVAL '3 year'
    ORDER BY et
    """
    bars_data = pg.query(query)
    return bars_data



@app.callback(
    Output('performance-chart', 'figure'),
    [Input('performance-data', 'data')]
)
def update_performance_chart(perf_data):
    fig = go.Figure()
    try:
        perf_df = pd.DataFrame(perf_data)
        perf_df = perf_df.pivot(columns='product', index='tradeDate', values='dailyPL').ffill().cumsum()
        perf_df['TotalPL'] = perf_df.sum(axis=1)
        
        colors = [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
            '#aec7e8', '#ffbb78', '#98df8a', '#ff9896', '#c5b0d5',
            '#c49c94', '#f7b6d3', '#c7c7c7', '#dbdb8d', '#9edae5'
        ]
        
        color_index = 0
        
        for col in perf_df.columns:
            if col == 'TotalPL':
                # Make TotalPL line thicker and use a distinctive color
                fig.add_trace(go.Scatter(
                    x=perf_df.index,
                    y=perf_df[col],
                    mode='lines',
                    name=col,
                    line=dict(width=4, color='#18BC9C')  # Thicker line, minty green color
                ))
            else:
                # Use cycling colors for individual products
                fig.add_trace(go.Scatter(
                    x=perf_df.index,
                    y=perf_df[col],
                    mode='lines',
                    name=col,
                    line=dict(width=1, color=colors[color_index % len(colors)])
                ))
                color_index += 1
        
        fig.update_layout(
            title="Lifetime PL",
            xaxis_title="Date",
            yaxis_title="PL ($)",
            hovermode='x unified'
        )
    except Exception as e:
        print(f"Error updating performance chart: {e}")
    return fig


# Callback for tab content
@app.callback(
    Output('tab-content', 'children'),
    [
        # Input('tabs', 'active_tab'),
        Input('selected-product-store', 'data'),
        Input('bar-data', 'data')
    ]
)
def render_tab_content(selected_product, bar_data):
    if not selected_product:
        return html.Div("Please select a product")
    
    # if active_tab == 'chart-tab':
        # Get bars data
    bars = pd.DataFrame(bar_data)
    # Rename datetime column
    datetime_col = bars.columns[0]
    bars = bars.rename(columns={
        datetime_col: 'datetime',
        'Open': 'open',
        'High': 'high',
        'Low': 'low',
        'Close': 'close',
        'hmm_regime': 'regime'
        })
    bars['datetime'] = pd.to_datetime(bars['datetime'])
    bars = bars[['datetime', 'open', 'high', 'low', 'close', 'regime']]
    bars.sort_values('datetime', inplace=True)
    bars.reset_index(drop=True, inplace=True)
    
    # Create the figure
    fig = go.Figure()
    
    # Add close price line
    fig.add_trace(go.Scatter(
        x=bars['datetime'],
        y=bars['close'],
        mode='lines',
        name='Close Price',
        line=dict(color='#18BC9C', width=2)
    ))
    
    # Add regime shading
    regime_colors = {
        -1: 'rgba(255, 0, 0, 0.2)',    # Red for short regime
        0: 'rgba(128, 128, 128, 0.2)',  # Gray for neutral regime  
        1: 'rgba(0, 255, 0, 0.2)'     # Green for long regime
    }
    
    regime_labels = {
        -1: 'Short',
        0: 'Neutral',
        1: 'Long'
    }
    
    # Group consecutive periods with same regime
    regime_changes = bars['regime'].ne(bars['regime'].shift()).cumsum()
    regime_groups = bars.groupby(regime_changes)
    
    # Track which regimes we've added to legend
    added_to_legend = set()
    
    # Get the price range for proper shading
    y_min = bars['close'].min()
    y_max = bars['close'].max()
    y_range = y_max - y_min
    y_padding = y_range * 0.05  # 5% padding
    
    for i, (_, group) in enumerate(regime_groups):
        regime_value = group['regime'].iloc[0]
        if regime_value in regime_colors:
            # Add to legend only once per regime
            show_legend = regime_value not in added_to_legend
            if show_legend:
                added_to_legend.add(regime_value)
            
            # Get start and end dates
            x0 = group['datetime'].iloc[0]
            x1 = group['datetime'].iloc[-1]
            
            # Extend the end date to the next group's start (or slightly beyond for last group)
            groups_list = list(regime_groups)
            if i < len(groups_list) - 1:  # Not the last group
                next_group = groups_list[i + 1][1]
                x1 = next_group['datetime'].iloc[0]
            else:  # Last group - extend slightly
                time_diff = group['datetime'].iloc[-1] - group['datetime'].iloc[0]
                if len(group) > 1:
                    avg_interval = time_diff / (len(group) - 1)
                    x1 = group['datetime'].iloc[-1] + avg_interval
            
            fig.add_shape(
                type="rect",
                x0=x0,
                x1=x1,
                y0=y_min - y_padding,
                y1=y_max + y_padding,
                fillcolor=regime_colors[regime_value],
                layer="below",
                line_width=0,
            )
            
            # Add invisible trace for legend
            if show_legend:
                fig.add_trace(go.Scatter(
                    x=[None], 
                    y=[None],
                    mode='markers',
                    marker=dict(
                        size=10, 
                        color=regime_colors[regime_value].replace('0.2', '0.8'),
                        symbol='square'
                    ),
                    name=f"Regime: {regime_labels[regime_value]}",
                    showlegend=True
                ))
    
    # Update layout with auto-scaling
    fig.update_layout(
        title=dict(
            text=f"{selected_product} - Close Price with Regime Overlay",
            # y=0.95,  # Move title down (default is 0.98)
            x=0.5,   # Center horizontally
            xanchor='center',
            yanchor='top'
        ),
        xaxis_title="Date",
        yaxis_title="Price",
        showlegend=True,
        hovermode='x unified',
        # width=800,
        # height=300,
        margin=dict(t=40),
        # template="minty_dark",
        # legend=dict(
        #     orientation="h",  # Horizontal orientation
        #     yanchor="top",
        #     y=-0.1,          # Position below the chart
        #     xanchor="center",
        #     x=0.5            # Center horizontally
        # ),
        yaxis=dict(
            autorange=True,
            fixedrange=False
        )
    )
    
    # Add range selector and slider
    fig.update_layout(
        xaxis=dict(
            # rangeselector=dict(
            #     buttons=list([
            #         dict(count=1, label="1M", step="month", stepmode="backward"),
            #         dict(count=6, label="6M", step="month", stepmode="backward"),
            #         dict(count=1, label="1Y", step="year", stepmode="backward"),
            #         dict(step="all")
            #     ])
            # ),
            rangeslider=dict(visible=False),
            type="date"
        )
    )
        
    return dcc.Graph(
        figure=fig, 
        style={
            'width': '100%', 
            'height': '500px'
        },
        config={'responsive': True}
    )
    


if __name__ == '__main__':
    app.run(debug=True, port=8050)
